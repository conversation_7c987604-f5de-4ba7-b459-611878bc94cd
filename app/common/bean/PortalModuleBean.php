<?php
declare(strict_types=1);

namespace app\common\bean;

use orm\mapping\Column;
use orm\mapping\Id;
use orm\mapping\Table;
use orm\BaseBean;

/**
 * 门户模块Bean
 * @Table(name="portal_modules")
 */
class PortalModuleBean extends BaseBean
{
    /**
     * @Id(auto=true)
     * @Column(name="id", type="integer")
     */
    public mixed $id = null;

    /**
     * @Column(name="module_name", type="string")
     */
    public mixed $moduleName = null;

    /**
     * @Column(name="module_title", type="string")
     */
    public mixed $moduleTitle = null;

    /**
     * @Column(name="module_description", type="string")
     */
    public mixed $moduleDescription = null;

    /**
     * @Column(name="column_count", type="integer")
     */
    public mixed $columnCount = null;

    /**
     * @Column(name="config_data", type="string")
     */
    public mixed $configData = null;

    /**
     * @Column(name="is_enabled", type="integer")
     */
    public mixed $isEnabled = null;

    /**
     * @Column(name="sort_order", type="integer")
     */
    public mixed $sortOrder = null;

    /**
     * @Column(name="create_time", type="datetime")
     */
    public mixed $createTime = null;

    /**
     * @Column(name="update_time", type="datetime")
     */
    public mixed $updateTime = null;

    // 启用状态常量
    const ENABLED_YES = 1;  // 启用
    const ENABLED_NO = 0;   // 禁用

    // 列数常量
    const COLUMN_SINGLE = 1; // 单列
    const COLUMN_DOUBLE = 2; // 双列

    /**
     * 获取所有启用状态
     */
    public static function getEnabledStatuses(): array
    {
        return [
            self::ENABLED_NO => '禁用',
            self::ENABLED_YES => '启用'
        ];
    }

    /**
     * 获取所有列数选项
     */
    public static function getColumnCounts(): array
    {
        return [
            self::COLUMN_SINGLE => '单列',
            self::COLUMN_DOUBLE => '双列'
        ];
    }

    /**
     * 获取启用状态文本
     */
    public function getEnabledText(): string
    {
        $statuses = self::getEnabledStatuses();
        return $statuses[$this->isEnabled] ?? '未知';
    }

    /**
     * 获取列数文本
     */
    public function getColumnCountText(): string
    {
        $counts = self::getColumnCounts();
        return $counts[$this->columnCount] ?? '未知';
    }

    /**
     * 检查是否启用
     */
    public function isModuleEnabled(): bool
    {
        return $this->isEnabled == self::ENABLED_YES;
    }

    /**
     * 检查是否单列
     */
    public function isSingleColumn(): bool
    {
        return $this->columnCount == self::COLUMN_SINGLE;
    }

    /**
     * 检查是否双列
     */
    public function isDoubleColumn(): bool
    {
        return $this->columnCount == self::COLUMN_DOUBLE;
    }

    /**
     * 获取配置数据（解析JSON）
     */
    public function getParsedConfigData(): array
    {
        if (empty($this->configData)) {
            return [];
        }
        
        $data = json_decode($this->configData, true);
        return is_array($data) ? $data : [];
    }

    /**
     * 设置配置数据（转换为JSON）
     */
    public function setConfigData(array $data): void
    {
        $this->configData = json_encode($data, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 验证列数
     */
    public function validateColumnCount(): bool
    {
        return in_array($this->columnCount, [self::COLUMN_SINGLE, self::COLUMN_DOUBLE]);
    }

    /**
     * 验证启用状态
     */
    public function validateEnabledStatus(): bool
    {
        return in_array($this->isEnabled, [self::ENABLED_YES, self::ENABLED_NO]);
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'module_name' => $this->moduleName,
            'module_title' => $this->moduleTitle,
            'module_description' => $this->moduleDescription,
            'column_count' => $this->columnCount,
            'column_count_text' => $this->getColumnCountText(),
            'config_data' => $this->configData,
            'parsed_config_data' => $this->getParsedConfigData(),
            'is_enabled' => $this->isEnabled,
            'enabled_text' => $this->getEnabledText(),
            'sort_order' => $this->sortOrder,
            'create_time' => $this->createTime,
            'update_time' => $this->updateTime,
            'is_module_enabled' => $this->isModuleEnabled(),
            'is_single_column' => $this->isSingleColumn(),
            'is_double_column' => $this->isDoubleColumn()
        ];
    }
}

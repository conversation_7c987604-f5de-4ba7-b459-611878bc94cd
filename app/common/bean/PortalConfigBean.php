<?php
declare(strict_types=1);

namespace app\common\bean;

use orm\mapping\Column;
use orm\mapping\Id;
use orm\mapping\Table;
use orm\BaseBean;

/**
 * 门户配置Bean
 * @Table(name="portal_configs")
 */
class PortalConfigBean extends BaseBean
{
    /**
     * @Id(auto=true)
     * @Column(name="id", type="integer")
     */
    public mixed $id = null;

    /**
     * @Column(name="config_key", type="string")
     */
    public mixed $configKey = null;

    /**
     * @Column(name="config_value", type="string")
     */
    public mixed $configValue = null;

    /**
     * @Column(name="config_type", type="string")
     */
    public mixed $configType = null;

    /**
     * @Column(name="group_name", type="string")
     */
    public mixed $groupName = null;

    /**
     * @Column(name="description", type="string")
     */
    public mixed $description = null;

    /**
     * @Column(name="sort_order", type="integer")
     */
    public mixed $sortOrder = null;

    /**
     * @Column(name="is_system", type="integer")
     */
    public mixed $isSystem = null;

    /**
     * @Column(name="create_time", type="datetime")
     */
    public mixed $createTime = null;

    /**
     * @Column(name="update_time", type="datetime")
     */
    public mixed $updateTime = null;

    // 配置类型常量
    const TYPE_STRING = 'string';
    const TYPE_NUMBER = 'number';
    const TYPE_BOOLEAN = 'boolean';
    const TYPE_JSON = 'json';

    // 系统配置常量
    const SYSTEM_YES = 1; // 系统配置
    const SYSTEM_NO = 0;  // 非系统配置

    /**
     * 获取所有配置类型
     */
    public static function getConfigTypes(): array
    {
        return [
            self::TYPE_STRING => '字符串',
            self::TYPE_NUMBER => '数字',
            self::TYPE_BOOLEAN => '布尔值',
            self::TYPE_JSON => 'JSON'
        ];
    }

    /**
     * 获取系统配置状态
     */
    public static function getSystemStatuses(): array
    {
        return [
            self::SYSTEM_NO => '普通配置',
            self::SYSTEM_YES => '系统配置'
        ];
    }

    /**
     * 获取配置类型文本
     */
    public function getConfigTypeText(): string
    {
        $types = self::getConfigTypes();
        return $types[$this->configType] ?? '未知';
    }

    /**
     * 获取系统配置状态文本
     */
    public function getSystemStatusText(): string
    {
        $statuses = self::getSystemStatuses();
        return $statuses[$this->isSystem] ?? '未知';
    }

    /**
     * 检查是否为系统配置
     */
    public function isSystemConfig(): bool
    {
        return $this->isSystem == self::SYSTEM_YES;
    }

    /**
     * 根据类型转换配置值
     */
    public function getConvertedValue()
    {
        if (is_null($this->configValue)) {
            return null;
        }

        switch ($this->configType) {
            case self::TYPE_NUMBER:
                return is_numeric($this->configValue) ? (float)$this->configValue : 0;
            
            case self::TYPE_BOOLEAN:
                return in_array(strtolower($this->configValue), ['true', '1', 'yes', 'on']);
            
            case self::TYPE_JSON:
                $decoded = json_decode($this->configValue, true);
                return is_array($decoded) ? $decoded : [];
            
            case self::TYPE_STRING:
            default:
                return (string)$this->configValue;
        }
    }

    /**
     * 设置配置值（根据类型自动转换）
     */
    public function setConvertedValue($value): void
    {
        switch ($this->configType) {
            case self::TYPE_NUMBER:
                $this->configValue = (string)$value;
                break;
            
            case self::TYPE_BOOLEAN:
                $this->configValue = $value ? 'true' : 'false';
                break;
            
            case self::TYPE_JSON:
                $this->configValue = is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : (string)$value;
                break;
            
            case self::TYPE_STRING:
            default:
                $this->configValue = (string)$value;
                break;
        }
    }

    /**
     * 验证配置类型
     */
    public function validateConfigType(): bool
    {
        return in_array($this->configType, [
            self::TYPE_STRING, 
            self::TYPE_NUMBER, 
            self::TYPE_BOOLEAN, 
            self::TYPE_JSON
        ]);
    }

    /**
     * 验证系统配置状态
     */
    public function validateSystemStatus(): bool
    {
        return in_array($this->isSystem, [self::SYSTEM_YES, self::SYSTEM_NO]);
    }

    /**
     * 验证配置键名格式
     */
    public function validateConfigKey(): bool
    {
        return !empty($this->configKey) && preg_match('/^[a-zA-Z][a-zA-Z0-9_]*$/', $this->configKey);
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'config_key' => $this->configKey,
            'config_value' => $this->configValue,
            'converted_value' => $this->getConvertedValue(),
            'config_type' => $this->configType,
            'config_type_text' => $this->getConfigTypeText(),
            'group_name' => $this->groupName,
            'description' => $this->description,
            'sort_order' => $this->sortOrder,
            'is_system' => $this->isSystem,
            'system_status_text' => $this->getSystemStatusText(),
            'create_time' => $this->createTime,
            'update_time' => $this->updateTime,
            'is_system_config' => $this->isSystemConfig()
        ];
    }
}

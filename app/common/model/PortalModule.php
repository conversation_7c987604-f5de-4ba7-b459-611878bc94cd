<?php

namespace app\common\model;

use think\Exception;
use think\Model;

/**
 * 门户模块模型
 */
class PortalModule extends Model
{
    // 表名
    protected $name = 'portal_modules';
    
    // 设置字段信息
    protected $schema = [
        'id'                 => 'int',
        'module_name'        => 'string',
        'module_title'       => 'string',
        'module_description' => 'string',
        'column_count'       => 'int',
        'config_data'        => 'json',
        'is_enabled'         => 'int',
        'sort_order'         => 'int',
        'create_time'        => 'datetime',
        'update_time'        => 'datetime',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 类型转换
    protected $type = [
        'id'           => 'integer',
        'column_count' => 'integer',
        'is_enabled'   => 'boolean',
        'sort_order'   => 'integer',
        'config_data'  => 'json',
    ];
    
    // 允许写入的字段
    protected $field = [
        'module_name', 'module_title', 'module_description',
        'column_count', 'config_data', 'is_enabled', 'sort_order'
    ];
    
    // JSON字段
    protected $json = ['config_data'];
    
    /**
     * 获取启用的模块列表
     *
     * @return array
     */
    public static function getEnabledModules(): array
    {
        return self::where('is_enabled', 1)
                  ->order('sort_order', 'asc')
                  ->select()
                  ->toArray();
    }
    
    /**
     * 获取所有模块列表
     *
     * @return array
     */
    public static function getAllModules(): array
    {
        return self::order('sort_order', 'asc')
                  ->select()
                  ->toArray();
    }
    
    /**
     * 根据模块名获取模块
     *
     * @param string $moduleName 模块名称
     * @return PortalModule|null
     */
    public static function getByName(string $moduleName): ?PortalModule
    {
        return self::where('module_name', $moduleName)->find();
    }
    
    /**
     * 更新模块排序
     *
     * @param array $sortData 排序数据 [['id' => 1, 'sort_order' => 1], ...]
     * @return bool
     */
    public static function updateSort(array $sortData): bool
    {
        try {
            self::startTrans();
            
            foreach ($sortData as $item) {
                if (isset($item['id']) && isset($item['sort_order'])) {
                    self::where('id', $item['id'])->update(['sort_order' => $item['sort_order']]);
                }
            }
            
            self::commit();
            return true;
        } catch (Exception $e) {
            self::rollback();
            return false;
        }
    }
    
    /**
     * 切换模块启用状态
     *
     * @param int $id 模块ID
     * @return bool
     */
    public static function toggleStatus(int $id): bool
    {
        $module = self::find($id);
        if (!$module) {
            return false;
        }
        
        $module->is_enabled = !$module->is_enabled;
        return $module->save();
    }
    
    /**
     * 验证列数
     *
     * @param int $columnCount 列数
     * @return bool
     */
    public static function validateColumnCount(int $columnCount): bool
    {
        return in_array($columnCount, [1, 2]);
    }
    
    /**
     * 获取模块配置数据
     *
     * @param string $moduleName 模块名称
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function getModuleConfig(string $moduleName, string $key = '', $default = null)
    {
        $module = self::getByName($moduleName);
        
        if (!$module || !$module->is_enabled) {
            return $default;
        }
        
        $configData = $module->config_data ?: [];
        
        if (empty($key)) {
            return $configData;
        }
        
        return $configData[$key] ?? $default;
    }
    
    /**
     * 设置模块配置数据
     *
     * @param string $moduleName 模块名称
     * @param array $configData 配置数据
     * @return bool
     */
    public static function setModuleConfig(string $moduleName, array $configData): bool
    {
        $module = self::getByName($moduleName);
        
        if (!$module) {
            return false;
        }
        
        $module->config_data = $configData;
        return $module->save();
    }
    
    /**
     * 创建新模块
     *
     * @param array $data 模块数据
     * @return PortalModule|false
     */
    public static function createModule(array $data)
    {
        // 验证必填字段
        if (empty($data['module_name']) || empty($data['module_title'])) {
            return false;
        }
        
        // 检查模块名是否已存在
        if (self::where('module_name', $data['module_name'])->find()) {
            return false;
        }
        
        // 验证列数
        if (isset($data['column_count']) && !self::validateColumnCount($data['column_count'])) {
            return false;
        }
        
        // 设置默认值
        $data['column_count'] = $data['column_count'] ?? 1;
        $data['is_enabled'] = $data['is_enabled'] ?? true;
        $data['sort_order'] = $data['sort_order'] ?? self::getNextSortOrder();
        
        return self::create($data);
    }
    
    /**
     * 获取下一个排序号
     *
     * @return int
     */
    protected static function getNextSortOrder(): int
    {
        $maxSort = self::max('sort_order');
        return ($maxSort ?: 0) + 1;
    }
    
    /**
     * 转换为数组（包含格式化的配置数据）
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = parent::toArray();
        
        // 确保config_data是数组格式
        if (isset($data['config_data']) && is_string($data['config_data'])) {
            $data['config_data'] = json_decode($data['config_data'], true) ?: [];
        }
        
        return $data;
    }
}

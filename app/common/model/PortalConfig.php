<?php

namespace app\common\model;

use think\Exception;
use think\Model;
use think\model\concern\SoftDelete;

/**
 * 门户配置模型
 */
class PortalConfig extends Model
{
    // 表名
    protected $name = 'portal_configs';
    
    // 设置字段信息
    protected $schema = [
        'id'           => 'int',
        'config_key'   => 'string',
        'config_value' => 'string',
        'config_type'  => 'string',
        'group_name'   => 'string',
        'description'  => 'string',
        'sort_order'   => 'int',
        'is_system'    => 'int',
        'create_time'  => 'datetime',
        'update_time'  => 'datetime',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 类型转换
    protected $type = [
        'id'         => 'integer',
        'sort_order' => 'integer',
        'is_system'  => 'boolean',
    ];
    
    // 允许写入的字段
    protected $field = [
        'config_key', 'config_value', 'config_type', 'group_name',
        'description', 'sort_order', 'is_system'
    ];
    
    /**
     * 获取配置值（根据类型自动转换）
     *
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function getValue(string $key, $default = null)
    {
        $config = self::where('config_key', $key)->find();
        
        if (!$config) {
            return $default;
        }
        
        return self::convertValue($config->config_value, $config->config_type);
    }
    
    /**
     * 设置配置值
     *
     * @param string $key 配置键名
     * @param mixed $value 配置值
     * @param string $type 配置类型
     * @param string $group 配置分组
     * @param string $description 配置描述
     * @return bool
     */
    public static function setValue(string $key, $value, string $type = 'string', string $group = 'default', string $description = ''): bool
    {
        $data = [
            'config_key'   => $key,
            'config_value' => self::formatValue($value, $type),
            'config_type'  => $type,
            'group_name'   => $group,
            'description'  => $description,
        ];
        
        $config = self::where('config_key', $key)->find();
        
        if ($config) {
            return $config->save($data);
        } else {
            return self::create($data) ? true : false;
        }
    }
    
    /**
     * 根据分组获取配置
     *
     * @param string $group 分组名称
     * @return array
     */
    public static function getByGroup(string $group): array
    {
        try {
            $configs = self::where('group_name', $group)
                          ->order('sort_order', 'asc')
                          ->select();

            $result = [];
            if ($configs) {
                foreach ($configs as $config) {
                    $result[$config->config_key] = self::convertValue($config->config_value, $config->config_type);
                }
            }

            return $result;
        } catch (Exception $e) {
            // 如果查询失败，返回空数组
            return [];
        }
    }
    
    /**
     * 获取所有配置（按分组）
     *
     * @return array
     */
    public static function getAllGrouped(): array
    {
        $configs = self::order('group_name,sort_order')->select();
        
        $result = [];
        foreach ($configs as $config) {
            $group = $config->group_name;
            if (!isset($result[$group])) {
                $result[$group] = [];
            }
            $result[$group][$config->config_key] = [
                'value' => self::convertValue($config->config_value, $config->config_type),
                'type' => $config->config_type,
                'description' => $config->description,
                'is_system' => $config->is_system,
            ];
        }
        
        return $result;
    }
    
    /**
     * 值类型转换
     *
     * @param string $value 原始值
     * @param string $type 类型
     * @return mixed
     */
    protected static function convertValue(string $value, string $type)
    {
        switch ($type) {
            case 'number':
                return is_numeric($value) ? (strpos($value, '.') !== false ? (float)$value : (int)$value) : 0;
            case 'boolean':
                return in_array(strtolower($value), ['true', '1', 'yes', 'on']);
            case 'json':
                return json_decode($value, true) ?: [];
            default:
                return $value;
        }
    }
    
    /**
     * 格式化值用于存储
     *
     * @param mixed $value 值
     * @param string $type 类型
     * @return string
     */
    protected static function formatValue($value, string $type): string
    {
        switch ($type) {
            case 'boolean':
                return $value ? 'true' : 'false';
            case 'json':
                return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : (string)$value;
            default:
                return (string)$value;
        }
    }
    
    /**
     * 删除配置（系统配置不允许删除）
     *
     * @param string $key 配置键名
     * @return bool
     * @throws \Exception
     */
    public static function deleteConfig(string $key): bool
    {
        $config = self::where('config_key', $key)->find();
        
        if (!$config) {
            throw new \Exception('配置不存在');
        }
        
        if ($config->is_system) {
            throw new \Exception('系统配置不允许删除');
        }
        
        return $config->delete();
    }
}

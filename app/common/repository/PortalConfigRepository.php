<?php
declare(strict_types=1);

namespace app\common\repository;

use app\common\bean\PortalConfigBean;
use orm\EntityManager;
use think\exception\ValidateException;

/**
 * 门户配置Repository
 */
class PortalConfigRepository
{
    /**
     * 根据ID查找配置
     */
    public function findById(int $id): ?PortalConfigBean
    {
        $bean = new PortalConfigBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 根据配置键查找配置
     */
    public function findByKey(string $configKey): ?PortalConfigBean
    {
        $bean = new PortalConfigBean();
        $result = EntityManager::create($bean)
            ->where('config_key', $configKey)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 根据分组获取配置列表
     */
    public function findByGroup(string $groupName): array
    {
        $bean = new PortalConfigBean();
        return EntityManager::create($bean)
            ->where('group_name', $groupName)
            ->order('sort_order', 'asc')
            ->order('id', 'asc')
            ->selectResult();
    }

    /**
     * 获取所有配置
     */
    public function findAll(): array
    {
        $bean = new PortalConfigBean();
        return EntityManager::create($bean)
            ->order('group_name', 'asc')
            ->order('sort_order', 'asc')
            ->order('id', 'asc')
            ->selectResult();
    }

    /**
     * 分页获取配置列表
     */
    public function findWithPagination(array $where = [], int $page = 1, int $limit = 20): array
    {
        $bean = new PortalConfigBean();
        $query = EntityManager::create($bean);
        
        // 构建查询条件
        foreach ($where as $field => $value) {
            if (!is_null($value) && $value !== '') {
                switch ($field) {
                    case 'config_key':
                    case 'description':
                        $query->whereLike($field, '%' . $value . '%');
                        break;
                    default:
                        $query->where($field, $value);
                        break;
                }
            }
        }
        
        // 计算总数
        $total = $query->count();
        
        // 分页查询
        $offset = ($page - 1) * $limit;
        $list = $query->order('group_name', 'asc')
                     ->order('sort_order', 'asc')
                     ->order('id', 'asc')
                     ->limit($offset, $limit)
                     ->selectResult();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }

    /**
     * 获取配置值（根据类型自动转换）
     */
    public function getValue(string $configKey, $default = null)
    {
        $config = $this->findByKey($configKey);
        
        if (!$config) {
            return $default;
        }
        
        return $config->getConvertedValue();
    }

    /**
     * 根据分组获取配置键值对
     */
    public function getGroupValues(string $groupName): array
    {
        $configs = $this->findByGroup($groupName);
        $result = [];
        
        foreach ($configs as $config) {
            $result[$config->configKey] = $config->getConvertedValue();
        }
        
        return $result;
    }

    /**
     * 创建配置
     */
    public function create(PortalConfigBean $config): PortalConfigBean
    {
        // 验证数据
        $this->validateConfig($config);
        
        // 检查配置键是否已存在
        if ($this->existsByKey($config->configKey, 0)) {
            throw new ValidateException('配置键已存在');
        }
        
        // 设置默认值
        if (is_null($config->configType)) {
            $config->configType = PortalConfigBean::TYPE_STRING;
        }
        if (is_null($config->groupName)) {
            $config->groupName = 'default';
        }
        if (is_null($config->sortOrder)) {
            $config->sortOrder = $this->getNextSortOrder($config->groupName);
        }
        if (is_null($config->isSystem)) {
            $config->isSystem = PortalConfigBean::SYSTEM_NO;
        }
        
        $bean = new PortalConfigBean();
        $result = EntityManager::create($bean)
            ->insert([
                'config_key' => $config->configKey,
                'config_value' => $config->configValue,
                'config_type' => $config->configType,
                'group_name' => $config->groupName,
                'description' => $config->description,
                'sort_order' => $config->sortOrder,
                'is_system' => $config->isSystem,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        if ($result) {
            return $this->findById($result);
        }
        
        throw new ValidateException('创建配置失败');
    }

    /**
     * 更新配置
     */
    public function update(PortalConfigBean $config): PortalConfigBean
    {
        if (!$config->id) {
            throw new ValidateException('配置ID不能为空');
        }
        
        // 验证数据
        $this->validateConfig($config);
        
        // 检查配置键是否已存在（排除自己）
        if ($this->existsByKey($config->configKey, $config->id)) {
            throw new ValidateException('配置键已存在');
        }
        
        $bean = new PortalConfigBean();
        $result = EntityManager::create($bean)
            ->where('id', $config->id)
            ->update([
                'config_key' => $config->configKey,
                'config_value' => $config->configValue,
                'config_type' => $config->configType,
                'group_name' => $config->groupName,
                'description' => $config->description,
                'sort_order' => $config->sortOrder,
                'is_system' => $config->isSystem,
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        if ($result !== false) {
            return $this->findById($config->id);
        }
        
        throw new ValidateException('更新配置失败');
    }

    /**
     * 设置配置值
     */
    public function setValue(string $configKey, $value): bool
    {
        $config = $this->findByKey($configKey);
        
        if (!$config) {
            return false;
        }
        
        $config->setConvertedValue($value);
        
        $bean = new PortalConfigBean();
        $result = EntityManager::create($bean)
            ->where('id', $config->id)
            ->update([
                'config_value' => $config->configValue,
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        return $result !== false;
    }

    /**
     * 删除配置
     */
    public function delete(int $id): bool
    {
        // 检查是否为系统配置
        $config = $this->findById($id);
        if ($config && $config->isSystemConfig()) {
            throw new ValidateException('系统配置不能删除');
        }
        
        $bean = new PortalConfigBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->delete();
        
        return $result > 0;
    }

    /**
     * 检查配置键是否存在
     */
    public function existsByKey(string $configKey, int $excludeId = 0): bool
    {
        $query = EntityManager::create(new PortalConfigBean())
            ->where('config_key', $configKey);
        
        if ($excludeId > 0) {
            $query->where('id', '<>', $excludeId);
        }
        
        return $query->count() > 0;
    }

    /**
     * 获取分组列表
     */
    public function getGroups(): array
    {
        $bean = new PortalConfigBean();
        $groups = EntityManager::create($bean)
            ->field('group_name')
            ->group('group_name')
            ->order('group_name', 'asc')
            ->column('group_name');
        
        return $groups ?: [];
    }

    /**
     * 获取下一个排序号
     */
    public function getNextSortOrder(string $groupName = ''): int
    {
        $query = EntityManager::create(new PortalConfigBean());
        
        if (!empty($groupName)) {
            $query->where('group_name', $groupName);
        }
        
        $maxSort = $query->max('sort_order');
        return ($maxSort ?: 0) + 1;
    }

    /**
     * 验证配置数据
     */
    private function validateConfig(PortalConfigBean $config): void
    {
        if (empty($config->configKey)) {
            throw new ValidateException('配置键不能为空');
        }
        
        if (!$config->validateConfigKey()) {
            throw new ValidateException('配置键格式无效，只能包含字母、数字和下划线，且必须以字母开头');
        }
        
        if (!$config->validateConfigType()) {
            throw new ValidateException('配置类型无效');
        }
        
        if (!$config->validateSystemStatus()) {
            throw new ValidateException('系统配置状态值无效');
        }
    }
}

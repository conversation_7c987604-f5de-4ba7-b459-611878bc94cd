<?php
declare(strict_types=1);

namespace app\common\repository;

use app\common\bean\PortalModuleBean;
use orm\EntityManager;
use think\Exception;
use think\exception\ValidateException;

/**
 * 门户模块Repository
 */
class PortalModuleRepository
{
    /**
     * 根据ID查找模块
     */
    public function findById(int $id): ?PortalModuleBean
    {
        $bean = new PortalModuleBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 根据模块名查找模块
     */
    public function findByName(string $moduleName): ?PortalModuleBean
    {
        $bean = new PortalModuleBean();
        $result = EntityManager::create($bean)
            ->where('module_name', $moduleName)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 获取所有模块
     */
    public function findAll(bool $onlyEnabled = false): array
    {
        $bean = new PortalModuleBean();
        $query = EntityManager::create($bean);
        
        if ($onlyEnabled) {
            $query->where('is_enabled', PortalModuleBean::ENABLED_YES);
        }
        
        return $query->order('sort_order', 'asc')
                    ->order('id', 'asc')
                    ->selectResult();
    }

    /**
     * 获取启用的模块列表
     */
    public function findEnabled(): array
    {
        return $this->findAll(true);
    }

    /**
     * 分页获取模块列表
     */
    public function findWithPagination(array $where = [], int $page = 1, int $limit = 20): array
    {
        $bean = new PortalModuleBean();
        $query = EntityManager::create($bean);
        
        // 构建查询条件
        foreach ($where as $field => $value) {
            if (!is_null($value) && $value !== '') {
                switch ($field) {
                    case 'module_name':
                    case 'module_title':
                    case 'module_description':
                        $query->whereLike($field, '%' . $value . '%');
                        break;
                    default:
                        $query->where($field, $value);
                        break;
                }
            }
        }
        
        // 计算总数
        $total = $query->count();
        
        // 分页查询
        $offset = ($page - 1) * $limit;
        $list = $query->order('sort_order', 'asc')
                     ->order('id', 'asc')
                     ->limit($offset, $limit)
                     ->selectResult();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }

    /**
     * 创建模块
     */
    public function create(PortalModuleBean $module): PortalModuleBean
    {
        // 验证数据
        $this->validateModule($module);
        
        // 检查模块名是否已存在
        if ($this->existsByName($module->moduleName, 0)) {
            throw new ValidateException('模块名称已存在');
        }
        
        // 设置默认值
        if (is_null($module->columnCount)) {
            $module->columnCount = PortalModuleBean::COLUMN_SINGLE;
        }
        if (is_null($module->isEnabled)) {
            $module->isEnabled = PortalModuleBean::ENABLED_YES;
        }
        if (is_null($module->sortOrder)) {
            $module->sortOrder = $this->getNextSortOrder();
        }
        
        $bean = new PortalModuleBean();
        $result = EntityManager::create($bean)
            ->insert([
                'module_name' => $module->moduleName,
                'module_title' => $module->moduleTitle,
                'module_description' => $module->moduleDescription,
                'column_count' => $module->columnCount,
                'config_data' => $module->configData,
                'is_enabled' => $module->isEnabled,
                'sort_order' => $module->sortOrder,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        if ($result) {
            return $this->findById($result);
        }
        
        throw new ValidateException('创建模块失败');
    }

    /**
     * 更新模块
     */
    public function update(PortalModuleBean $module): PortalModuleBean
    {
        if (!$module->id) {
            throw new ValidateException('模块ID不能为空');
        }
        
        // 验证数据
        $this->validateModule($module);
        
        // 检查模块名是否已存在（排除自己）
        if ($this->existsByName($module->moduleName, $module->id)) {
            throw new ValidateException('模块名称已存在');
        }
        
        $bean = new PortalModuleBean();
        $result = EntityManager::create($bean)
            ->where('id', $module->id)
            ->update([
                'module_name' => $module->moduleName,
                'module_title' => $module->moduleTitle,
                'module_description' => $module->moduleDescription,
                'column_count' => $module->columnCount,
                'config_data' => $module->configData,
                'is_enabled' => $module->isEnabled,
                'sort_order' => $module->sortOrder,
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        if ($result !== false) {
            return $this->findById($module->id);
        }
        
        throw new ValidateException('更新模块失败');
    }

    /**
     * 删除模块
     */
    public function delete(int $id): bool
    {
        $bean = new PortalModuleBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->delete();
        
        return $result > 0;
    }

    /**
     * 检查模块名是否存在
     */
    public function existsByName(string $moduleName, int $excludeId = 0): bool
    {
        $query = EntityManager::create(new PortalModuleBean())
            ->where('module_name', $moduleName);
        
        if ($excludeId > 0) {
            $query->where('id', '<>', $excludeId);
        }
        
        return $query->count() > 0;
    }

    /**
     * 获取下一个排序号
     */
    public function getNextSortOrder(): int
    {
        $bean = new PortalModuleBean();
        $maxSort = EntityManager::create($bean)->max('sort_order');
        return ($maxSort ?: 0) + 1;
    }

    /**
     * 更新模块排序
     */
    public function updateSort(array $sortData): bool
    {
        try {
            foreach ($sortData as $item) {
                if (isset($item['id']) && isset($item['sort_order'])) {
                    $bean = new PortalModuleBean();
                    EntityManager::create($bean)
                        ->where('id', $item['id'])
                        ->update([
                            'sort_order' => $item['sort_order'],
                            'update_time' => date('Y-m-d H:i:s')
                        ]);
                }
            }
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 切换模块启用状态
     */
    public function toggleStatus(int $id): bool
    {
        $module = $this->findById($id);
        if (!$module) {
            return false;
        }
        
        $newStatus = $module->isEnabled == PortalModuleBean::ENABLED_YES 
            ? PortalModuleBean::ENABLED_NO 
            : PortalModuleBean::ENABLED_YES;
        
        $bean = new PortalModuleBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->update([
                'is_enabled' => $newStatus,
                'update_time' => date('Y-m-d H:i:s')
            ]);
        
        return $result !== false;
    }

    /**
     * 验证模块数据
     */
    private function validateModule(PortalModuleBean $module): void
    {
        if (empty($module->moduleName)) {
            throw new ValidateException('模块名称不能为空');
        }
        
        if (empty($module->moduleTitle)) {
            throw new ValidateException('模块标题不能为空');
        }
        
        if (!$module->validateColumnCount()) {
            throw new ValidateException('列数必须为1或2');
        }
        
        if (!$module->validateEnabledStatus()) {
            throw new ValidateException('启用状态值无效');
        }
        
        // 验证模块名格式
        if (!preg_match('/^[a-zA-Z][a-zA-Z0-9_]*$/', $module->moduleName)) {
            throw new ValidateException('模块名称格式无效，只能包含字母、数字和下划线，且必须以字母开头');
        }
    }
}

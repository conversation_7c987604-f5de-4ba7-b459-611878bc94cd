<?php
declare(strict_types=1);

namespace app\common\repository;

use app\common\bean\ArticleCategoryBean;
use orm\EntityManager;
use think\Exception;
use think\exception\ValidateException;

/**
 * 文章分类Repository
 */
class ArticleCategoryRepository
{
    /**
     * 根据ID查找分类
     */
    public function findById(int $id): ?ArticleCategoryBean
    {
        $bean = new ArticleCategoryBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 根据slug查找分类
     */
    public function findBySlug(string $slug): ?ArticleCategoryBean
    {
        $bean = new ArticleCategoryBean();
        $result = EntityManager::create($bean)
            ->where('slug', $slug)
            ->findResult();
        
        return $result && !$result->isEmpty() ? $result : null;
    }

    /**
     * 根据父级ID获取子分类
     */
    public function findByParentId(int $parentId, bool $onlyVisible = false): array
    {
        $bean = new ArticleCategoryBean();
        $query = EntityManager::create($bean)
            ->where('parent_id', $parentId);
        
        if ($onlyVisible) {
            $query->where('is_show', ArticleCategoryBean::SHOW_VISIBLE);
        }
        
        return $query->order('sort_order', 'desc')
            ->order('id', 'asc')
            ->selectResult();
    }

    /**
     * 获取所有顶级分类
     */
    public function findTopLevel(bool $onlyVisible = false): array
    {
        return $this->findByParentId(0, $onlyVisible);
    }

    /**
     * 根据路径获取所有子分类（包括子子分类）
     */
    public function findByPath(string $path): array
    {
        $bean = new ArticleCategoryBean();
        return EntityManager::create($bean)
            ->where('path', 'like', $path . '%')
            ->order('level', 'asc')
            ->order('sort_order', 'desc')
            ->selectResult();
    }

    /**
     * 检查slug是否存在
     */
    public function existsBySlug(string $slug, int $excludeId = 0): bool
    {
        $query = EntityManager::create(new ArticleCategoryBean())
            ->where('slug', $slug);
        
        if ($excludeId > 0) {
            $query->where('id', '<>', $excludeId);
        }
        
        return $query->count() > 0;
    }

    /**
     * 检查是否有子分类
     */
    public function hasChildren(int $id): bool
    {
        return EntityManager::create(new ArticleCategoryBean())
            ->where('parent_id', $id)
            ->count() > 0;
    }

    /**
     * 创建分类
     */
    public function create(ArticleCategoryBean $category): int
    {
        // 设置创建时间
        $category->createTime = date('Y-m-d H:i:s');
        $category->updateTime = date('Y-m-d H:i:s');
        
        $id = EntityManager::create($category)->insertBeanGetId($category);
        $category->id = $id;
        $category->stored();
        
        return $id;
    }

    /**
     * 更新分类
     */
    public function update(ArticleCategoryBean $category): bool
    {
        if ($category->isEmpty() || !$category->id) {
            throw new ValidateException('分类不存在或ID为空');
        }
        
        // 设置更新时间
        $category->updateTime = date('Y-m-d H:i:s');
        
        $result = EntityManager::create($category)
            ->where('id', $category->id)
            ->updateBean($category);
        
        return $result > 0;
    }

    /**
     * 删除分类
     */
    public function delete(int $id): bool
    {
        $bean = new ArticleCategoryBean();
        $result = EntityManager::create($bean)
            ->where('id', $id)
            ->delete();
        
        return $result > 0;
    }

    /**
     * 批量更新路径
     */
    public function batchUpdatePath(array $updates): bool
    {
        try {
            foreach ($updates as $update) {
                $bean = new ArticleCategoryBean();
                EntityManager::create($bean)
                    ->where('id', $update['id'])
                    ->update([
                        'path' => $update['path'],
                        'level' => $update['level'],
                        'update_time' => date('Y-m-d H:i:s')
                    ]);
            }
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 获取分类列表（支持分页和筛选）
     */
    public function getList(int $page = 1, int $limit = 15, array $where = []): array
    {
        $bean = new ArticleCategoryBean();
        $query = EntityManager::create($bean);
        
        // 添加查询条件
        if (!empty($where['name'])) {
            $query->where('name', 'like', '%' . $where['name'] . '%');
        }
        if (!empty($where['type'])) {
            $query->where('type', $where['type']);
        }
        if (!empty($where['parent_id'])) {
            $query->where('parent_id', $where['parent_id']);
        }
        if (isset($where['is_show']) && $where['is_show'] !== '') {
            $query->where('is_show', $where['is_show']);
        }
        if (!empty($where['level'])) {
            $query->where('level', $where['level']);
        }
        
        // 分页
        $offset = ($page - 1) * $limit;
        $list = $query->limit($offset, $limit)
            ->order('level', 'asc')
            ->order('sort_order', 'desc')
            ->order('id', 'asc')
            ->selectResult();
        
        // 获取总数
        $total = EntityManager::create($bean);
        if (!empty($where['name'])) {
            $total->where('name', 'like', '%' . $where['name'] . '%');
        }
        if (!empty($where['type'])) {
            $total->where('type', $where['type']);
        }
        if (!empty($where['parent_id'])) {
            $total->where('parent_id', $where['parent_id']);
        }
        if (isset($where['is_show']) && $where['is_show'] !== '') {
            $total->where('is_show', $where['is_show']);
        }
        if (!empty($where['level'])) {
            $total->where('level', $where['level']);
        }
        $totalCount = $total->count();
        
        return [
            'list' => $list,
            'total' => $totalCount,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($totalCount / $limit)
        ];
    }

    /**
     * 获取所有分类（用于构建树形结构）
     */
    public function findAll(bool $onlyVisible = false): array
    {
        $bean = new ArticleCategoryBean();
        $query = EntityManager::create($bean);
        
        if ($onlyVisible) {
            $query->where('is_show', ArticleCategoryBean::SHOW_VISIBLE);
        }
        
        return $query->order('level', 'asc')
            ->order('sort_order', 'desc')
            ->order('id', 'asc')
            ->selectResult();
    }

    /**
     * 获取面包屑路径
     */
    public function getBreadcrumb(int $id): array
    {
        $category = $this->findById($id);
        if (!$category || empty($category->path)) {
            return [];
        }

        $pathIds = $category->getPathArray();
        if (empty($pathIds)) {
            return [];
        }

        $bean = new ArticleCategoryBean();
        return EntityManager::create($bean)
            ->where('id', 'in', $pathIds)
            ->order('level', 'asc')
            ->selectResult();
    }

    /**
     * 获取最大排序值
     */
    public function getMaxSortOrder(int $parentId = 0): int
    {
        $result = EntityManager::create(new ArticleCategoryBean())
            ->where('parent_id', $parentId)
            ->max('sort_order');

        return (int)$result;
    }

    /**
     * 检查循环引用
     */
    public function checkCircularReference(int $id, int $parentId): bool
    {
        if ($id === $parentId) {
            return true;
        }

        $parent = $this->findById($parentId);
        if (!$parent || empty($parent->path)) {
            return false;
        }

        $pathIds = $parent->getPathArray();
        return in_array($id, $pathIds);
    }

    /**
     * 根据类型获取分类
     */
    public function findByType(string $type, bool $onlyVisible = false): array
    {
        $bean = new ArticleCategoryBean();
        $query = EntityManager::create($bean)
            ->where('type', $type);

        if ($onlyVisible) {
            $query->where('is_show', ArticleCategoryBean::SHOW_VISIBLE);
        }

        return $query->order('level', 'asc')
            ->order('sort_order', 'desc')
            ->order('id', 'asc')
            ->selectResult();
    }
}

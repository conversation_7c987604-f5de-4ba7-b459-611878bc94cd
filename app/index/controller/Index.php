<?php

namespace app\index\controller;

use app\common\bean\ArticleBean;
use app\common\repository\ArticleCategoryRepository;
use app\common\repository\ArticleRepository;
use app\common\repository\PortalModuleRepository;
use app\common\repository\PortalConfigRepository;
use app\common\service\ArticleService;
use think\App;
use think\Exception;
use think\exception\HttpException;
use think\facade\View;

class Index extends Base
{
    protected $articleService;
    protected $articleRepository;
    protected $categoryRepository;
    protected $siteConfigService;
    protected $moduleRepository;
    protected $configRepository;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->articleService = new ArticleService();
        $this->articleRepository = new ArticleRepository();
        $this->categoryRepository = new ArticleCategoryRepository();
        $this->moduleRepository = new PortalModuleRepository();
        $this->configRepository = new PortalConfigRepository();
    }

    /**
     * 首页
     */
    public function index()
    {
        try {
            // 获取分类列表
            $categoriesData = $this->categoryRepository->findAll(true);
            $categories = array_map(function($category) {
                return $category->toArray();
            }, $categoriesData);

            // 获取模块配置
            $modulesData = $this->moduleRepository->findEnabled();
            $modules = array_map(function($module) {
                return $module->toArray();
            }, $modulesData);

            // 获取各类新闻数据
            $bannerResult = $this->articleRepository->findWithPagination(['status' => 'published'], 1, 5);
            $hotResult = $this->articleRepository->findWithPagination(['status' => 'published'], 1, 10);
            $latestResult = $this->articleRepository->findWithPagination(['status' => 'published'], 1, 12);

            $newsData = [
                'banner' => array_map(function($article) {
                    return $article->toArray();
                }, $bannerResult['list']),
                'hot' => array_map(function($article) {
                    return $article->toArray();
                }, $hotResult['list']),
                'latest' => array_map(function($article) {
                    return $article->toArray();
                }, $latestResult['list'])
            ];

            // 获取网站基础配置
            $basicConfigsData = $this->configRepository->getGroupValues('site');

            // 传递数据到视图
            View::assign([
                'title' => '首页 - 新闻门户',
                'categories' => $categories,
                'modules' => $modules,
                'newsData' => $newsData,
                'basicConfigs' => $basicConfigsData,
                'seoConfigs' => $basicConfigsData // 兼容模板中的seoConfigs变量
            ]);

            return View::fetch();
        } catch (Exception $e) {
            // 记录错误日志
            trace('首页加载失败: ' . $e->getMessage(), 'error');

            // 显示错误信息
            View::assign('error', '页面加载失败，请稍后再试');
            return View::fetch();
        }
    }

    /**
     * 文章详情页
     * 
     * @param int $id 文章ID
     */
    public function article($id)
    {
        try {
            // 获取文章详情
            $article = $this->articleService->getById((int)$id);
            
            if (!$article || $article->status !== ArticleBean::STATUS_PUBLISHED) {
                throw new HttpException(404, '文章不存在');
            }
            
            // 更新阅读量
            $this->articleRepository->incrementViewCount($id);
            
            // 获取相关文章
            $relatedArticles = $this->articleRepository->getRelatedArticles($article->categoryId, $article->id);
            
            // 获取分类列表
            $categories = $this->categoryRepository->findAll(true);
            
            // 传递数据到视图
            View::assign([
                'article' => $article,
                'relatedArticles' => $relatedArticles,
                'categories' => $categories
            ]);
            
            return View::fetch();
        } catch (HttpException $e) {
            throw $e;
        } catch (Exception $e) {
            // 记录错误日志
            trace('文章详情页加载失败: ' . $e->getMessage(), 'error');
            
            // 显示错误信息
            View::assign('error', '文章加载失败，请稍后再试');
            return View::fetch('error/index');
        }
    }

    /**
     * 分类文章列表页
     * 
     * @param int $id 分类ID
     */
    public function category($id)
    {
        try {
            // 获取分类信息
            $category = $this->categoryRepository->findById((int)$id);
            
            if (!$category || !$category->isShow) {
                throw new HttpException(404, '分类不存在');
            }
            
            // 获取分页参数
            $page = (int)$this->request->param('page', 1);
            $limit = 10;
            
            // 获取分类下的文章
            $where = [
                'category_id' => $id,
                'status' => ArticleBean::STATUS_PUBLISHED
            ];
            $result = $this->articleRepository->findPublished($page, $limit, $where);
            
            // 获取分类列表
            $categories = $this->categoryRepository->findAll(true);
            
            // 传递数据到视图
            View::assign([
                'category' => $category,
                'articles' => $result['list'],
                'pagination' => [
                    'total' => $result['total'],
                    'current_page' => $result['page'],
                    'last_page' => $result['pages'],
                ],
                'categories' => $categories
            ]);
            
            return View::fetch();
        } catch (HttpException $e) {
            throw $e;
        } catch (Exception $e) {
            // 记录错误日志
            trace('分类页加载失败: ' . $e->getMessage(), 'error');
            
            // 显示错误信息
            View::assign('error', '分类页加载失败，请稍后再试');
            return View::fetch('error/index');
        }
    }

    /**
     * 搜索页面
     */
    public function search()
    {
        try {
            // 获取搜索关键词
            $keyword = $this->request->param('keyword', '');
            
            // 获取分页参数
            $page = (int)$this->request->param('page', 1);
            $limit = 10;
            
            // 如果关键词为空，重定向到首页
            if (empty($keyword)) {
                return redirect('/');
            }
            
            // 搜索文章
            $where = ['title' => $keyword];
            $result = $this->articleRepository->findPublished($page, $limit, $where);

            // 获取分类列表
            $categories = $this->categoryRepository->findAll(true);
            
            // 传递数据到视图
            View::assign([
                'keyword' => $keyword,
                'articles' => $result['list'],
                'pagination' => [
                    'total' => $result['total'],
                    'current_page' => $result['page'],
                    'last_page' => $result['pages'],
                ],
                'categories' => $categories
            ]);
            
            return View::fetch();
        } catch (Exception $e) {
            // 记录错误日志
            trace('搜索页加载失败: ' . $e->getMessage(), 'error');
            
            // 显示错误信息
            View::assign('error', '搜索失败，请稍后再试');
            return View::fetch('error/index');
        }
    }
}

<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// 门户首页路由 - 根目录直接显示门户页面
Route::group('index', function () { // 注意这里，将 '/' 改为 'index'
    Route::get('category/:id', 'Index/category');
    Route::get('article/:id', 'Index/article');
})->pattern([
    'id' => '\d+'
]);

// 针对首页的特殊处理，确保根目录依然指向 Index/index
Route::get('/', 'Index/index');


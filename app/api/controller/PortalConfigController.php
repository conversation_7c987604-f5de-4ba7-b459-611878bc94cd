<?php

namespace app\api\controller;

use app\BaseController;
use app\common\service\PortalConfigService;
use app\common\service\PortalModuleService;
use app\api\annotation\RequireRole;
use think\Request;
use think\Response;
use think\exception\ValidateException;

/**
 * 门户配置控制器
 */
class PortalConfigController extends BaseController
{
    protected PortalConfigService $configService;
    protected PortalModuleService $moduleService;

    public function __construct()
    {
        parent::__construct();
        $this->configService = new PortalConfigService();
        $this->moduleService = new PortalModuleService();
    }
    
    /**
     * 获取配置列表
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '获取配置列表需要管理员权限')]
    public function index(Request $request): Response
    {
        try {
            $page = (int)$request->param('page', 1);
            $limit = (int)$request->param('limit', 20);
            $group = $request->param('group', '');
            $keyword = $request->param('keyword', '');
            
            $result = $this->configService->getConfigList($page, $limit, $group, $keyword);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取配置列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取配置分组列表
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '获取配置分组需要管理员权限')]
    public function groups(Request $request): Response
    {
        try {
            $groups = $this->configService->getConfigGroups();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $groups
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取配置分组失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 根据分组获取配置
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '获取分组配置需要管理员权限')]
    public function getByGroup(Request $request): Response
    {
        try {
            $group = $request->param('group');
            if (empty($group)) {
                throw new ValidateException('分组名称不能为空');
            }
            
            $configs = $this->configService->getConfigByGroup($group);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $configs
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取分组配置失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取单个配置
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '获取配置详情需要管理员权限')]
    public function read(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('配置ID不能为空');
            }
            
            $config = $this->configService->getConfigById($id);
            if (!$config) {
                throw new ValidateException('配置不存在');
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $config->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取配置详情失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 创建配置
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole('super_admin', message: '创建配置需要超级管理员权限')]
    public function create(Request $request): Response
    {
        try {
            $data = $request->only([
                'config_key', 'config_value', 'config_type', 'group_name',
                'description', 'sort_order', 'is_system'
            ]);
            
            $config = $this->configService->createConfig($data);
            
            return json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $config->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建配置失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 更新配置
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '更新配置需要管理员权限')]
    public function update(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('配置ID不能为空');
            }
            
            $data = $request->only([
                'config_key', 'config_value', 'config_type', 'group_name',
                'description', 'sort_order', 'is_system'
            ]);
            
            $config = $this->configService->updateConfig($id, $data);
            
            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $config->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新配置失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 删除配置
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole('super_admin', message: '删除配置需要超级管理员权限')]
    public function delete(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('配置ID不能为空');
            }
            
            $result = $this->configService->deleteConfig($id);
            
            return json([
                'code' => 200,
                'message' => '删除成功',
                'data' => null
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除配置失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 批量设置配置
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '批量设置配置需要管理员权限')]
    public function batchSet(Request $request): Response
    {
        try {
            $configs = $request->param('configs', []);
            $group = $request->param('group', 'default');
            
            if (empty($configs) || !is_array($configs)) {
                throw new ValidateException('配置数据不能为空');
            }
            
            $result = $this->configService->batchSetConfigs($configs, $group);
            
            return json([
                'code' => 200,
                'message' => '批量设置成功',
                'data' => null
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '批量设置配置失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 清除配置缓存
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole('super_admin', message: '清除缓存需要超级管理员权限')]
    public function clearCache(Request $request): Response
    {
        try {
            $this->configService->clearAllCache();
            
            return json([
                'code' => 200,
                'message' => '缓存清除成功',
                'data' => null
            ]);
            
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '清除缓存失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    // ==================== 公开接口（无需权限） ====================

    /**
     * 获取启用的门户模块
     */
    public function enabledModules(): Response
    {
        try {
            $modules = $this->moduleService->getEnabledModules();

            return json([
                'status' => 'success',
                'message' => '获取启用模块成功',
                'data' => [
                    'modules' => $modules,
                    'total' => count($modules),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取启用模块失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 根据分组获取配置
     */
    public function configsByGroup($group): Response
    {
        try {
            $configs = $this->configService->getConfigByGroup($group);

            return json([
                'status' => 'success',
                'message' => '获取配置成功',
                'data' => [
                    'group' => $group,
                    'configs' => $configs,
                    'total' => count($configs),
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取配置失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取门户首页数据
     */
    public function homeData(): Response
    {
        try {
            // 获取启用的模块
            $modules = $this->moduleService->getEnabledModules();

            // 获取基础配置
            $siteConfigs = $this->configService->getConfigByGroup('site');

            return json([
                'status' => 'success',
                'message' => '获取首页数据成功',
                'data' => [
                    'modules' => $modules,
                    'site_configs' => $siteConfigs,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'message' => '获取首页数据失败',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

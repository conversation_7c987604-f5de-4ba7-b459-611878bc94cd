<?php

namespace app\api\controller;

use app\BaseController;
use app\common\service\PortalModuleService;
use app\api\annotation\RequireRole;
use think\Exception;
use think\Request;
use think\Response;
use think\exception\ValidateException;

/**
 * 门户模块控制器
 */
class PortalModuleController extends BaseController
{
    protected PortalModuleService $moduleService;
    
    public function __construct()
    {
        parent::__construct();
        $this->moduleService = new PortalModuleService();
    }
    
    /**
     * 获取模块列表
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '获取模块列表需要管理员权限')]
    public function index(Request $request): Response
    {
        try {
            $page = (int)$request->param('page', 1);
            $limit = (int)$request->param('limit', 20);
            $keyword = $request->param('keyword', '');
            $onlyEnabled = (bool)$request->param('only_enabled', false);
            
            $result = $this->moduleService->getModuleList($page, $limit, $keyword, $onlyEnabled);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $result
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取模块列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取启用的模块列表（用于前端展示）
     *
     * @param Request $request
     * @return Response
     */
    public function enabled(Request $request): Response
    {
        try {
            $modules = $this->moduleService->getEnabledModules();
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $modules
            ]);
            
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取启用模块失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取单个模块
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '获取模块详情需要管理员权限')]
    public function read(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('模块ID不能为空');
            }
            
            $module = $this->moduleService->getModuleById($id);
            if (!$module) {
                throw new ValidateException('模块不存在');
            }
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $module->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取模块详情失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 创建模块
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '创建模块需要管理员权限')]
    public function create(Request $request): Response
    {
        try {
            $data = $request->only([
                'module_name', 'module_title', 'module_description',
                'column_count', 'config_data', 'is_enabled', 'sort_order'
            ]);
            
            $module = $this->moduleService->createModule($data);
            
            return json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $module->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建模块失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 更新模块
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '更新模块需要管理员权限')]
    public function update(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('模块ID不能为空');
            }
            
            $data = $request->only([
                'module_name', 'module_title', 'module_description',
                'column_count', 'config_data', 'is_enabled', 'sort_order'
            ]);
            
            $module = $this->moduleService->updateModule($id, $data);
            
            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $module->toArray()
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新模块失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 删除模块
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '删除模块需要管理员权限')]
    public function delete(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('模块ID不能为空');
            }
            
            $result = $this->moduleService->deleteModule($id);
            
            return json([
                'code' => 200,
                'message' => '删除成功',
                'data' => null
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除模块失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 切换模块启用状态
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '切换模块状态需要管理员权限')]
    public function toggleStatus(Request $request): Response
    {
        try {
            $id = (int)$request->param('id');
            if (!$id) {
                throw new ValidateException('模块ID不能为空');
            }
            
            $result = $this->moduleService->toggleModuleStatus($id);
            
            return json([
                'code' => 200,
                'message' => '状态切换成功',
                'data' => null
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '切换模块状态失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 更新模块排序
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '更新模块排序需要管理员权限')]
    public function updateSort(Request $request): Response
    {
        try {
            $sortData = $request->param('sort_data', []);
            
            if (empty($sortData) || !is_array($sortData)) {
                throw new ValidateException('排序数据不能为空');
            }
            
            $result = $this->moduleService->updateModuleSort($sortData);
            
            return json([
                'code' => 200,
                'message' => '排序更新成功',
                'data' => null
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新模块排序失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取模块配置
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '获取模块配置需要管理员权限')]
    public function getConfig(Request $request): Response
    {
        try {
            $moduleName = $request->param('module_name');
            if (empty($moduleName)) {
                throw new ValidateException('模块名称不能为空');
            }
            
            $config = $this->moduleService->getModuleConfig($moduleName);
            
            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $config
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取模块配置失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 设置模块配置
     *
     * @param Request $request
     * @return Response
     */
    #[RequireRole(message: '设置模块配置需要管理员权限')]
    public function setConfig(Request $request): Response
    {
        try {
            $moduleName = $request->param('module_name');
            $configData = $request->param('config_data', []);
            
            if (empty($moduleName)) {
                throw new ValidateException('模块名称不能为空');
            }
            
            if (!is_array($configData)) {
                throw new ValidateException('配置数据必须是数组格式');
            }
            
            $result = $this->moduleService->setModuleConfig($moduleName, $configData);
            
            return json([
                'code' => 200,
                'message' => '配置设置成功',
                'data' => null
            ]);
            
        } catch (ValidateException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null
            ], 400);
        } catch (Exception $e) {
            return json([
                'code' => 500,
                'message' => '设置模块配置失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}

<?php
use think\facade\Route;

// API根路径 - 提供API信息
Route::get('/', 'app\api\controller\TestController@index');

// 测试路由
Route::get('test', 'app\api\controller\TestController@index');
Route::get('health', 'app\api\controller\TestController@health');

// 认证路由
Route::group('auth', function () {
    Route::get('check', 'app\api\controller\AuthController@check');
    Route::post('login', 'app\api\controller\AuthController@login');
    Route::get('login', 'app\api\controller\AuthController@login'); // 同时支持GET请求用于测试
});

// 分类管理路由 (无注解版本)
Route::group('categories', function () {
    Route::get('tree', 'app\api\controller\SimpleCategoryController@tree');
    Route::get('/', 'app\api\controller\SimpleCategoryController@index');
    Route::get('<id>', 'app\api\controller\SimpleCategoryController@read');
});

// 文章管理路由 (合并后的路由)
Route::group('articles', function () {
    // 公开接口（无需权限）
    Route::get('hot', 'app\api\controller\ArticleController@hot');
    Route::get('latest', 'app\api\controller\ArticleController@latest');
    Route::get('banner', 'app\api\controller\ArticleController@banner');
    Route::get('search', 'app\api\controller\ArticleController@search');
    Route::get('public', 'app\api\controller\ArticleController@publicIndex');
    Route::get('public/<id>', 'app\api\controller\ArticleController@publicRead');

    // 管理员接口（需要权限）
    Route::get('/', 'app\api\controller\ArticleController@index');
    Route::get('<id>', 'app\api\controller\ArticleController@read');
    Route::post('/', 'app\api\controller\ArticleController@create');
    Route::put('<id>', 'app\api\controller\ArticleController@update');
    Route::delete('<id>', 'app\api\controller\ArticleController@delete');
});

// 分类管理路由 (合并后的路由)
Route::group('categories', function () {
    // 公开接口（无需权限）
    Route::get('public/tree', 'app\api\controller\ArticleCategoryController@publicTree');
    Route::get('public', 'app\api\controller\ArticleCategoryController@publicList');

    // 管理员接口（需要权限）
    Route::get('/', 'app\api\controller\ArticleCategoryController@index');
    Route::get('<id>', 'app\api\controller\ArticleCategoryController@read');
    Route::post('/', 'app\api\controller\ArticleCategoryController@create');
    Route::put('<id>', 'app\api\controller\ArticleCategoryController@update');
    Route::delete('<id>', 'app\api\controller\ArticleCategoryController@delete');
    Route::get('stats', 'app\api\controller\ArticleCategoryController@adminStats');
    Route::post('system-manage', 'app\api\controller\ArticleCategoryController@systemManage');
});

// 门户配置API路由 (合并后的路由)
Route::group('portal', function () {
    // 公开接口（无需权限）
    Route::get('modules/enabled', 'app\api\controller\PortalConfigController@enabledModules');
    Route::get('configs/<group>', 'app\api\controller\PortalConfigController@configsByGroup');
    Route::get('home-data', 'app\api\controller\PortalConfigController@homeData');

    // 管理员接口（需要权限）
    Route::get('configs', 'app\api\controller\PortalConfigController@index');
    Route::get('configs/<id>', 'app\api\controller\PortalConfigController@read');
    Route::post('configs', 'app\api\controller\PortalConfigController@create');
    Route::put('configs/<id>', 'app\api\controller\PortalConfigController@update');
    Route::delete('configs/<id>', 'app\api\controller\PortalConfigController@delete');
});
